<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 招聘公告系统测试控制器
 */
class TestController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 测试页面
     */
    public function index()
    {
        echo "<!DOCTYPE html><html><head><meta charset='utf-8'><title>系统测试</title></head><body>";
        echo "<h1>招聘公告智能筛选系统测试</h1>";
        echo "<p>系统时间：" . date('Y-m-d H:i:s') . "</p>";
        echo "<p>当前URL：" . $_SERVER['REQUEST_URI'] . "</p>";

        // 测试数据库连接
        try {
            $db = M();
            echo "<p style='color: green;'>✓ 数据库连接正常</p>";

            // 测试表是否存在
            $tables = [
                'z_recruitment_notice' => '招聘公告表',
                'z_post_requirements' => '岗位要求表',
                'z_resume_post_match' => '匹配结果表'
            ];

            foreach ($tables as $table => $desc) {
                try {
                    $exists = $db->query("SHOW TABLES LIKE '{$table}'");
                    if ($exists) {
                        echo "<p style='color: green;'>✓ {$desc} ({$table}) 存在</p>";
                    } else {
                        echo "<p style='color: red;'>✗ {$desc} ({$table}) 不存在</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>✗ 检查 {$desc} 失败：" . $e->getMessage() . "</p>";
                }
            }

        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ 数据库连接失败：" . $e->getMessage() . "</p>";
        }

        // 测试模型
        try {
            $model = D('RecruitmentNotice');
            if ($model) {
                echo "<p style='color: green;'>✓ RecruitmentNotice 模型加载成功</p>";
            } else {
                echo "<p style='color: red;'>✗ RecruitmentNotice 模型加载失败</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ RecruitmentNotice 模型加载失败：" . $e->getMessage() . "</p>";
        }

        echo "<hr>";
        echo "<h2>快速链接</h2>";
        echo "<p><a href='" . U('recruitmentnotice/index') . "'>招聘公告列表</a></p>";
        echo "<p><a href='" . U('systemcheck/index') . "'>系统检查</a></p>";
        echo "<p><a href='" . U('test/simple') . "'>简单测试</a></p>";

        echo "</body></html>";
        exit;
    }

    /**
     * 简单测试
     */
    public function simple()
    {
        echo "简单测试成功！控制器工作正常。<br>";
        echo "时间：" . date('Y-m-d H:i:s') . "<br>";
        echo "<a href='" . U('test/index') . "'>返回测试首页</a>";
        exit;
    }

    /**
     * 创建测试数据
     */
    public function createTestData()
    {
        try {
            // 创建测试招聘公告
            $noticeModel = D('RecruitmentNotice');
            $noticeData = [
                'title' => '测试招聘公告 - ' . date('Y-m-d H:i:s'),
                'description' => '这是一个测试用的招聘公告，用于验证系统功能是否正常。',
                'company_name' => '测试公司',
                'status' => 1,
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $noticeId = $noticeModel->add($noticeData);
            
            if ($noticeId) {
                echo "<p style='color: green;'>✓ 测试招聘公告创建成功，ID: {$noticeId}</p>";
                echo "<p><a href='" . U('RecruitmentNotice/detail', ['id' => $noticeId]) . "'>查看测试公告</a></p>";
            } else {
                echo "<p style='color: red;'>✗ 测试招聘公告创建失败</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ 创建测试数据失败：" . $e->getMessage() . "</p>";
        }
        
        echo "<p><a href='" . U('Test/index') . "'>返回测试首页</a></p>";
        exit;
    }

    /**
     * 清理测试数据
     */
    public function cleanTestData()
    {
        try {
            $noticeModel = D('RecruitmentNotice');
            $count = $noticeModel->where(['company_name' => '测试公司'])->delete();
            
            echo "<p style='color: green;'>✓ 清理了 {$count} 条测试数据</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ 清理测试数据失败：" . $e->getMessage() . "</p>";
        }
        
        echo "<p><a href='" . U('Test/index') . "'>返回测试首页</a></p>";
        exit;
    }
}
