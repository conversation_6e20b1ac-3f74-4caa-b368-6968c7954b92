-- 招聘公告智能筛选系统菜单配置
-- 请在数据库中执行以下SQL语句来添加菜单

-- 1. 添加主菜单：招聘公告管理
INSERT INTO `z_menu` (`id`, `parentid`, `app`, `model`, `action`, `data`, `type`, `status`, `name`, `icon`, `remark`, `listorder`, `boot_id`) VALUES
(NULL, 0, 'Prime', 'RecruitmentNotice', 'index', '', 1, 1, '招聘公告管理', 'bullhorn', '招聘公告智能筛选系统', 50, 1);

-- 获取刚插入的主菜单ID（需要根据实际情况调整）
SET @parent_id = LAST_INSERT_ID();

-- 2. 添加子菜单：公告列表
INSERT INTO `z_menu` (`id`, `parentid`, `app`, `model`, `action`, `data`, `type`, `status`, `name`, `icon`, `remark`, `listorder`, `boot_id`) VALUES
(NULL, @parent_id, 'Prime', 'RecruitmentNotice', 'index', '', 1, 1, '公告列表', 'list', '查看所有招聘公告', 1, 1);

-- 3. 添加子菜单：新增公告
INSERT INTO `z_menu` (`id`, `parentid`, `app`, `model`, `action`, `data`, `type`, `status`, `name`, `icon`, `remark`, `listorder`, `boot_id`) VALUES
(NULL, @parent_id, 'Prime', 'RecruitmentNotice', 'edit', '', 1, 1, '新增公告', 'plus', '创建新的招聘公告', 2, 1);

-- 4. 添加子菜单：岗位要求配置
INSERT INTO `z_menu` (`id`, `parentid`, `app`, `model`, `action`, `data`, `type`, `status`, `name`, `icon`, `remark`, `listorder`, `boot_id`) VALUES
(NULL, @parent_id, 'Prime', 'PostRequirements', 'index', '', 1, 1, '岗位要求配置', 'cog', '配置岗位筛选要求', 3, 1);

-- 5. 添加子菜单：简历匹配结果
INSERT INTO `z_menu` (`id`, `parentid`, `app`, `model`, `action`, `data`, `type`, `status`, `name`, `icon`, `remark`, `listorder`, `boot_id`) VALUES
(NULL, @parent_id, 'Prime', 'ResumeMatch', 'index', '', 1, 1, '简历匹配结果', 'search', '查看简历匹配结果', 4, 1);

-- 6. 添加权限规则到 z_auth_rule 表
INSERT INTO `z_auth_rule` (`id`, `name`, `title`, `type`, `status`, `condition`) VALUES
(NULL, 'Prime/RecruitmentNotice/index', '招聘公告列表', 1, 1, ''),
(NULL, 'Prime/RecruitmentNotice/edit', '新增编辑招聘公告', 1, 1, ''),
(NULL, 'Prime/RecruitmentNotice/detail', '查看招聘公告详情', 1, 1, ''),
(NULL, 'Prime/RecruitmentNotice/delete', '删除招聘公告', 1, 1, ''),
(NULL, 'Prime/RecruitmentNotice/cgstat', '修改招聘公告状态', 1, 1, ''),
(NULL, 'Prime/RecruitmentNotice/posts', '管理招聘公告岗位', 1, 1, ''),
(NULL, 'Prime/RecruitmentNotice/batch', '批量操作招聘公告', 1, 1, ''),
(NULL, 'Prime/PostRequirements/index', '岗位要求配置列表', 1, 1, ''),
(NULL, 'Prime/PostRequirements/edit', '编辑岗位要求', 1, 1, ''),
(NULL, 'Prime/PostRequirements/batch_config', '批量配置岗位要求', 1, 1, ''),
(NULL, 'Prime/PostRequirements/copy_requirements', '复制岗位要求', 1, 1, ''),
(NULL, 'Prime/PostRequirements/delete', '删除岗位要求', 1, 1, ''),
(NULL, 'Prime/PostRequirements/preview', '预览筛选结果', 1, 1, ''),
(NULL, 'Prime/ResumeMatch/index', '简历匹配结果列表', 1, 1, ''),
(NULL, 'Prime/ResumeMatch/do_match', '执行简历匹配', 1, 1, ''),
(NULL, 'Prime/ResumeMatch/detail', '查看匹配详情', 1, 1, ''),
(NULL, 'Prime/ResumeMatch/post_matches', '查看岗位匹配结果', 1, 1, ''),
(NULL, 'Prime/ResumeMatch/export', '导出匹配结果', 1, 1, '');

-- 注意：
-- 1. 执行此SQL前请备份数据库
-- 2. 请根据实际的菜单ID调整 parentid 和 boot_id
-- 3. 如果需要为特定用户组分配权限，请在 z_auth_access 表中添加相应记录
-- 4. listorder 字段用于控制菜单显示顺序，可根据需要调整

-- 示例：为管理员用户组（假设ID为1）分配所有权限
-- INSERT INTO `z_auth_access` (`uid`, `group_id`) VALUES (1, 1);

-- 查询新添加的菜单
-- SELECT * FROM `z_menu` WHERE `name` LIKE '%招聘%' OR `name` LIKE '%公告%' OR `name` LIKE '%匹配%';
