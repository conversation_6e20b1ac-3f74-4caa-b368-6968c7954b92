<?php
namespace Common\Model;

use Common\Model\CommonModel;

/**
 * 简历岗位匹配模型
 */
class ResumePostMatchModel extends CommonModel
{
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['update_time', 'time', self::MODEL_UPDATE, 'function'],
    ];

    /**
     * 是否符合要求定义
     */
    public $is_qualified = [
        '0' => ['text' => '不符合', 'style' => 'danger'],
        '1' => ['text' => '符合', 'style' => 'success'],
    ];

    /**
     * 保存匹配结果
     * @param array $data 匹配数据
     * @return int|bool
     */
    public function saveMatch($data)
    {
        // 检查是否已存在
        $existing = $this->where([
            'user_job_id' => $data['user_job_id'],
            'post_id' => $data['post_id'],
            'notice_id' => $data['notice_id']
        ])->find();
        
        if ($existing) {
            // 更新
            $data['update_time'] = time();
            return $this->where(['id' => $existing['id']])->save($data);
        } else {
            // 新增
            $data['create_time'] = time();
            $data['update_time'] = time();
            return $this->add($data);
        }
    }

    /**
     * 获取招聘公告的匹配结果
     * @param int $noticeId 公告ID
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getNoticeMatches($noticeId, $filters = [], $page = 1, $limit = 20)
    {
        $offset = ($page - 1) * $limit;
        $where = ['rpm.notice_id' => $noticeId];
        
        // 添加筛选条件
        if (isset($filters['is_qualified']) && $filters['is_qualified'] !== '') {
            $where['rpm.is_qualified'] = $filters['is_qualified'];
        }
        if (isset($filters['min_score']) && $filters['min_score'] !== '') {
            $where['rpm.match_score'] = ['egt', $filters['min_score']];
        }
        if (isset($filters['max_score']) && $filters['max_score'] !== '') {
            if (isset($where['rpm.match_score'])) {
                $where['rpm.match_score'] = [
                    ['egt', $filters['min_score']],
                    ['elt', $filters['max_score']]
                ];
            } else {
                $where['rpm.match_score'] = ['elt', $filters['max_score']];
            }
        }
        if (isset($filters['post_id']) && $filters['post_id'] > 0) {
            $where['rpm.post_id'] = $filters['post_id'];
        }
        
        $matches = $this->alias('rpm')
            ->join('LEFT JOIN __USER_JOB__ uj ON rpm.user_job_id = uj.id')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rpm.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where($where)
            ->field('rpm.*, uj.name, uj.gender, uj.birthdate, uj.phone, uj.education_level, uj.major, uj.height, pp.job_name, p.name as project_name')
            ->order('rpm.match_score DESC, rpm.create_time DESC')
            ->limit($offset, $limit)
            ->select();
            
        foreach ($matches as &$match) {
            $match['is_qualified_info'] = $this->is_qualified[$match['is_qualified']];
            $match['create_time_format'] = date('Y-m-d H:i:s', $match['create_time']);
            
            // 计算年龄
            if ($match['birthdate']) {
                $match['age'] = date('Y') - date('Y', strtotime($match['birthdate']));
            }
            
            // 解析匹配详情
            if ($match['match_details']) {
                $match['match_details_array'] = json_decode($match['match_details'], true);
            }
        }
        
        return $matches;
    }

    /**
     * 获取岗位的匹配结果
     * @param int $postId 岗位ID
     * @param int $noticeId 公告ID
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getPostMatches($postId, $noticeId, $filters = [], $page = 1, $limit = 20)
    {
        $filters['post_id'] = $postId;
        return $this->getNoticeMatches($noticeId, $filters, $page, $limit);
    }

    /**
     * 获取简历的匹配岗位
     * @param int $userJobId 简历ID
     * @return array
     */
    public function getResumeMatches($userJobId)
    {
        $matches = $this->alias('rpm')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rpm.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->join('LEFT JOIN __RECRUITMENT_NOTICE__ rn ON rpm.notice_id = rn.id')
            ->where(['rpm.user_job_id' => $userJobId])
            ->field('rpm.*, pp.job_name, p.name as project_name, rn.title as notice_title')
            ->order('rpm.match_score DESC')
            ->select();
            
        foreach ($matches as &$match) {
            $match['is_qualified_info'] = $this->is_qualified[$match['is_qualified']];
            $match['create_time_format'] = date('Y-m-d H:i:s', $match['create_time']);
        }
        
        return $matches;
    }

    /**
     * 获取匹配统计信息
     * @param int $noticeId 公告ID
     * @return array
     */
    public function getMatchStats($noticeId)
    {
        $stats = [
            'total_matches' => 0,
            'qualified_matches' => 0,
            'unqualified_matches' => 0,
            'avg_score' => 0,
            'post_stats' => []
        ];
        
        // 总体统计
        $totalMatches = $this->where(['notice_id' => $noticeId])->count();
        $qualifiedMatches = $this->where(['notice_id' => $noticeId, 'is_qualified' => 1])->count();
        $avgScore = $this->where(['notice_id' => $noticeId])->avg('match_score');
        
        $stats['total_matches'] = $totalMatches;
        $stats['qualified_matches'] = $qualifiedMatches;
        $stats['unqualified_matches'] = $totalMatches - $qualifiedMatches;
        $stats['avg_score'] = round($avgScore, 2);
        
        // 按岗位统计
        $postStats = $this->alias('rpm')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rpm.post_id = pp.id')
            ->where(['rpm.notice_id' => $noticeId])
            ->field('rpm.post_id, pp.job_name, COUNT(*) as total_count, SUM(rpm.is_qualified) as qualified_count, AVG(rpm.match_score) as avg_score')
            ->group('rpm.post_id')
            ->select();
            
        foreach ($postStats as &$postStat) {
            $postStat['avg_score'] = round($postStat['avg_score'], 2);
            $postStat['unqualified_count'] = $postStat['total_count'] - $postStat['qualified_count'];
        }
        
        $stats['post_stats'] = $postStats;
        
        return $stats;
    }

    /**
     * 删除匹配记录
     * @param int $noticeId 公告ID
     * @param int $postId 岗位ID（可选）
     * @return bool
     */
    public function deleteMatches($noticeId, $postId = null)
    {
        $where = ['notice_id' => $noticeId];
        if ($postId) {
            $where['post_id'] = $postId;
        }
        
        return $this->where($where)->delete();
    }

    /**
     * 批量保存匹配结果
     * @param array $matches 匹配结果数组
     * @return bool
     */
    public function batchSaveMatches($matches)
    {
        if (empty($matches)) {
            return true;
        }
        
        // 使用事务处理
        $this->startTrans();
        
        try {
            foreach ($matches as $match) {
                if (!$this->saveMatch($match)) {
                    throw new \Exception('保存匹配结果失败');
                }
            }
            
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            $this->error = $e->getMessage();
            return false;
        }
    }
}
