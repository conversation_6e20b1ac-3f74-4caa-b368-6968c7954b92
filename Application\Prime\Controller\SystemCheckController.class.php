<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 招聘公告智能筛选系统检查控制器
 */
class SystemCheckController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 系统检查页面
     */
    public function index()
    {
        $checks = $this->performSystemCheck();
        
        $this->assign('checks', $checks);
        $this->display();
    }

    /**
     * 执行系统检查
     */
    private function performSystemCheck()
    {
        $checks = [];

        // 1. 检查数据库表
        $checks['database'] = $this->checkDatabaseTables();

        // 2. 检查模型文件
        $checks['models'] = $this->checkModelFiles();

        // 3. 检查控制器文件
        $checks['controllers'] = $this->checkControllerFiles();

        // 4. 检查视图文件
        $checks['views'] = $this->checkViewFiles();

        // 5. 检查菜单配置
        $checks['menus'] = $this->checkMenus();

        // 6. 检查权限配置
        $checks['permissions'] = $this->checkPermissions();

        // 7. 检查服务文件
        $checks['services'] = $this->checkServiceFiles();

        return $checks;
    }

    /**
     * 检查数据库表
     */
    private function checkDatabaseTables()
    {
        $tables = [
            'z_recruitment_notice' => '招聘公告表',
            'z_recruitment_notice_post' => '招聘公告岗位关联表',
            'z_post_requirements' => '岗位筛选要求表',
            'z_resume_post_match' => '简历岗位匹配表'
        ];

        $results = [];
        $db = M();

        foreach ($tables as $table => $description) {
            try {
                $exists = $db->query("SHOW TABLES LIKE '{$table}'");
                $results[] = [
                    'name' => $table,
                    'description' => $description,
                    'status' => !empty($exists),
                    'message' => !empty($exists) ? '表存在' : '表不存在'
                ];
            } catch (Exception $e) {
                $results[] = [
                    'name' => $table,
                    'description' => $description,
                    'status' => false,
                    'message' => '检查失败：' . $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 检查模型文件
     */
    private function checkModelFiles()
    {
        $models = [
            'Application/Common/Model/RecruitmentNoticeModel.class.php' => '招聘公告模型',
            'Application/Common/Model/PostRequirementsModel.class.php' => '岗位要求模型',
            'Application/Common/Model/ResumePostMatchModel.class.php' => '简历匹配模型',
            'Application/Common/Model/RecruitmentNoticePostModel.class.php' => '公告岗位关联模型'
        ];

        $results = [];
        foreach ($models as $file => $description) {
            $exists = file_exists($file);
            $results[] = [
                'name' => $file,
                'description' => $description,
                'status' => $exists,
                'message' => $exists ? '文件存在' : '文件不存在'
            ];
        }

        return $results;
    }

    /**
     * 检查控制器文件
     */
    private function checkControllerFiles()
    {
        $controllers = [
            'Application/Prime/Controller/RecruitmentNoticeController.class.php' => '招聘公告控制器',
            'Application/Prime/Controller/PostRequirementsController.class.php' => '岗位要求控制器',
            'Application/Prime/Controller/ResumeMatchController.class.php' => '简历匹配控制器'
        ];

        $results = [];
        foreach ($controllers as $file => $description) {
            $exists = file_exists($file);
            $results[] = [
                'name' => $file,
                'description' => $description,
                'status' => $exists,
                'message' => $exists ? '文件存在' : '文件不存在'
            ];
        }

        return $results;
    }

    /**
     * 检查视图文件
     */
    private function checkViewFiles()
    {
        $views = [
            'Application/Prime/View/RecruitmentNotice/index.html' => '招聘公告列表页',
            'Application/Prime/View/RecruitmentNotice/edit.html' => '招聘公告编辑页',
            'Application/Prime/View/RecruitmentNotice/detail.html' => '招聘公告详情页',
            'Application/Prime/View/RecruitmentNotice/posts.html' => '岗位关联页',
            'Application/Prime/View/PostRequirements/index.html' => '岗位要求列表页',
            'Application/Prime/View/PostRequirements/edit.html' => '岗位要求编辑页',
            'Application/Prime/View/ResumeMatch/index.html' => '匹配结果列表页',
            'Application/Prime/View/ResumeMatch/detail.html' => '匹配详情页'
        ];

        $results = [];
        foreach ($views as $file => $description) {
            $exists = file_exists($file);
            $results[] = [
                'name' => $file,
                'description' => $description,
                'status' => $exists,
                'message' => $exists ? '文件存在' : '文件不存在'
            ];
        }

        return $results;
    }

    /**
     * 检查菜单配置
     */
    private function checkMenus()
    {
        $menus = [
            '招聘公告管理',
            '公告列表',
            '新增公告',
            '岗位要求配置',
            '简历匹配结果'
        ];

        $results = [];
        foreach ($menus as $menuName) {
            $exists = M('Menu')->where(['name' => $menuName])->count() > 0;
            $results[] = [
                'name' => $menuName,
                'description' => '系统菜单',
                'status' => $exists,
                'message' => $exists ? '菜单已配置' : '菜单未配置'
            ];
        }

        return $results;
    }

    /**
     * 检查权限配置
     */
    private function checkPermissions()
    {
        $permissions = [
            'Prime/RecruitmentNotice/index' => '招聘公告列表权限',
            'Prime/RecruitmentNotice/edit' => '编辑招聘公告权限',
            'Prime/PostRequirements/index' => '岗位要求配置权限',
            'Prime/ResumeMatch/index' => '简历匹配结果权限'
        ];

        $results = [];
        foreach ($permissions as $rule => $description) {
            $exists = M('AuthRule')->where(['name' => $rule])->count() > 0;
            $results[] = [
                'name' => $rule,
                'description' => $description,
                'status' => $exists,
                'message' => $exists ? '权限已配置' : '权限未配置'
            ];
        }

        return $results;
    }

    /**
     * 检查服务文件
     */
    private function checkServiceFiles()
    {
        $services = [
            'Application/Common/Service/ResumeMatchService.class.php' => '简历匹配服务'
        ];

        $results = [];
        foreach ($services as $file => $description) {
            $exists = file_exists($file);
            $results[] = [
                'name' => $file,
                'description' => $description,
                'status' => $exists,
                'message' => $exists ? '文件存在' : '文件不存在'
            ];
        }

        return $results;
    }

    /**
     * 修复系统配置
     */
    public function fix()
    {
        $action = I('post.action');
        
        switch ($action) {
            case 'create_menus':
                $this->createMenus();
                break;
            case 'create_permissions':
                $this->createPermissions();
                break;
            default:
                $this->error('无效的修复操作');
        }
    }

    /**
     * 创建菜单
     */
    private function createMenus()
    {
        // 这里可以添加自动创建菜单的逻辑
        $this->success('菜单创建功能需要手动执行 menu_config.sql 文件');
    }

    /**
     * 创建权限
     */
    private function createPermissions()
    {
        // 这里可以添加自动创建权限的逻辑
        $this->success('权限创建功能需要手动执行 menu_config.sql 文件');
    }
}
