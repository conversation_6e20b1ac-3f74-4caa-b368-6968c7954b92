<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>招聘公告智能筛选系统检查</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            
            <div class="main">
                <!-- 系统概览 -->
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-info-circle"></i> 系统概览
                        </h3>
                    </div>
                    <div class="panel-body">
                        <p>招聘公告智能筛选系统部署检查工具，用于验证系统各组件是否正确安装和配置。</p>
                        <p><strong>检查时间：</strong>{:date('Y-m-d H:i:s')}</p>
                    </div>
                </div>

                <!-- 数据库表检查 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-database"></i> 数据库表检查
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>表名</th>
                                        <th>描述</th>
                                        <th>状态</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($checks['database'] as $item){</php>
                                    <tr>
                                        <td><code>{$item.name}</code></td>
                                        <td>{$item.description}</td>
                                        <td>
                                            <span class="label label-<php>echo $item['status'] ? 'success' : 'danger';</php>">
                                                <i class="fa fa-<php>echo $item['status'] ? 'check' : 'times';</php>"></i>
                                                <php>echo $item['status'] ? '正常' : '异常';</php>
                                            </span>
                                        </td>
                                        <td>{$item.message}</td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 模型文件检查 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-code"></i> 模型文件检查
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>文件路径</th>
                                        <th>描述</th>
                                        <th>状态</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($checks['models'] as $item){</php>
                                    <tr>
                                        <td><code>{$item.name}</code></td>
                                        <td>{$item.description}</td>
                                        <td>
                                            <span class="label label-<php>echo $item['status'] ? 'success' : 'danger';</php>">
                                                <i class="fa fa-<php>echo $item['status'] ? 'check' : 'times';</php>"></i>
                                                <php>echo $item['status'] ? '存在' : '缺失';</php>
                                            </span>
                                        </td>
                                        <td>{$item.message}</td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 控制器文件检查 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-cogs"></i> 控制器文件检查
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>文件路径</th>
                                        <th>描述</th>
                                        <th>状态</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($checks['controllers'] as $item){</php>
                                    <tr>
                                        <td><code>{$item.name}</code></td>
                                        <td>{$item.description}</td>
                                        <td>
                                            <span class="label label-<php>echo $item['status'] ? 'success' : 'danger';</php>">
                                                <i class="fa fa-<php>echo $item['status'] ? 'check' : 'times';</php>"></i>
                                                <php>echo $item['status'] ? '存在' : '缺失';</php>
                                            </span>
                                        </td>
                                        <td>{$item.message}</td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 视图文件检查 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-file-code-o"></i> 视图文件检查
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>文件路径</th>
                                        <th>描述</th>
                                        <th>状态</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($checks['views'] as $item){</php>
                                    <tr>
                                        <td><code>{$item.name}</code></td>
                                        <td>{$item.description}</td>
                                        <td>
                                            <span class="label label-<php>echo $item['status'] ? 'success' : 'danger';</php>">
                                                <i class="fa fa-<php>echo $item['status'] ? 'check' : 'times';</php>"></i>
                                                <php>echo $item['status'] ? '存在' : '缺失';</php>
                                            </span>
                                        </td>
                                        <td>{$item.message}</td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 菜单配置检查 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-sitemap"></i> 菜单配置检查
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>菜单名称</th>
                                        <th>描述</th>
                                        <th>状态</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($checks['menus'] as $item){</php>
                                    <tr>
                                        <td>{$item.name}</td>
                                        <td>{$item.description}</td>
                                        <td>
                                            <span class="label label-<php>echo $item['status'] ? 'success' : 'warning';</php>">
                                                <i class="fa fa-<php>echo $item['status'] ? 'check' : 'exclamation-triangle';</php>"></i>
                                                <php>echo $item['status'] ? '已配置' : '未配置';</php>
                                            </span>
                                        </td>
                                        <td>{$item.message}</td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-triangle"></i>
                            如果菜单未配置，请执行项目根目录下的 <code>menu_config.sql</code> 文件来添加菜单。
                        </div>
                    </div>
                </div>

                <!-- 权限配置检查 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-key"></i> 权限配置检查
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>权限规则</th>
                                        <th>描述</th>
                                        <th>状态</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($checks['permissions'] as $item){</php>
                                    <tr>
                                        <td><code>{$item.name}</code></td>
                                        <td>{$item.description}</td>
                                        <td>
                                            <span class="label label-<php>echo $item['status'] ? 'success' : 'warning';</php>">
                                                <i class="fa fa-<php>echo $item['status'] ? 'check' : 'exclamation-triangle';</php>"></i>
                                                <php>echo $item['status'] ? '已配置' : '未配置';</php>
                                            </span>
                                        </td>
                                        <td>{$item.message}</td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 服务文件检查 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-wrench"></i> 服务文件检查
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>文件路径</th>
                                        <th>描述</th>
                                        <th>状态</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($checks['services'] as $item){</php>
                                    <tr>
                                        <td><code>{$item.name}</code></td>
                                        <td>{$item.description}</td>
                                        <td>
                                            <span class="label label-<php>echo $item['status'] ? 'success' : 'danger';</php>">
                                                <i class="fa fa-<php>echo $item['status'] ? 'check' : 'times';</php>"></i>
                                                <php>echo $item['status'] ? '存在' : '缺失';</php>
                                            </span>
                                        </td>
                                        <td>{$item.message}</td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-tools"></i> 系统操作
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="location.reload()">
                                <i class="fa fa-refresh"></i> 重新检查
                            </button>
                            <a href="{:U('recruitmentnotice/index')}" class="btn btn-success">
                                <i class="fa fa-play"></i> 进入系统
                            </a>
                        </div>
                        
                        <div class="alert alert-info" style="margin-top: 20px;">
                            <h4><i class="fa fa-info-circle"></i> 部署说明</h4>
                            <ol>
                                <li>确保所有文件都已正确部署到对应目录</li>
                                <li>执行 <code>menu_config.sql</code> 文件添加菜单和权限</li>
                                <li>可选择执行 <code>demo_data.sql</code> 文件添加演示数据</li>
                                <li>确保相关用户具有访问权限</li>
                                <li>查看 <code>招聘公告智能筛选系统使用说明.md</code> 了解详细使用方法</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />
