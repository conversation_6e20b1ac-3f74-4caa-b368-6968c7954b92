<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 岗位要求配置控制器
 */
class PostRequirementsController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 岗位要求配置页面
     */
    public function index()
    {
        $notice_id = intval(I('get.notice_id'));
        if (!$notice_id) {
            $this->error('参数错误');
        }
        
        // 获取招聘公告信息
        $notice = D('RecruitmentNotice')->where(['id' => $notice_id])->find();
        if (!$notice) {
            $this->error('招聘公告不存在');
        }
        
        // 获取公告关联的岗位
        $posts = D('RecruitmentNotice')->getNoticePosts($notice_id);
        
        // 获取已配置的要求
        $requirements = D('PostRequirements')->getNoticeRequirements($notice_id);
        $requirementsMap = [];
        foreach ($requirements as $req) {
            $requirementsMap[$req['post_id']] = $req;
        }
        
        $this->assign('notice', $notice);
        $this->assign('posts', $posts);
        $this->assign('requirements_map', $requirementsMap);
        
        $this->display();
    }

    /**
     * 编辑岗位要求
     */
    public function edit()
    {
        $notice_id = intval(I('get.notice_id'));
        $post_id = intval(I('get.post_id'));
        
        if (!$notice_id || !$post_id) {
            $this->error('参数错误');
        }
        
        // 获取招聘公告信息
        $notice = D('RecruitmentNotice')->where(['id' => $notice_id])->find();
        if (!$notice) {
            $this->error('招聘公告不存在');
        }
        
        // 获取岗位信息
        $post = D('ProjectPost')->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['pp.id' => $post_id])
            ->field('pp.*, p.name as project_name')
            ->find();
        if (!$post) {
            $this->error('岗位不存在');
        }
        
        $obj = D('PostRequirements');
        
        // 获取现有要求
        $requirements = $obj->getRequirements($post_id, $notice_id);
        
        if (IS_POST) {
            $data = [
                'post_id' => $post_id,
                'notice_id' => $notice_id,
                'min_age' => intval(I('post.min_age')) ?: null,
                'max_age' => intval(I('post.max_age')) ?: null,
                'gender' => intval(I('post.gender', 0)),
                'min_height' => intval(I('post.min_height')) ?: null,
                'max_height' => intval(I('post.max_height')) ?: null,
                'education_level' => intval(I('post.education_level', 0)),
                'major_keywords' => trim(I('post.major_keywords')),
                'min_work_years' => intval(I('post.min_work_years')) ?: null,
                'max_work_years' => intval(I('post.max_work_years')) ?: null,
                'health_status' => trim(I('post.health_status')),
                'is_afraid_heights' => intval(I('post.is_afraid_heights', 0)),
                'political_status' => trim(I('post.political_status')),
                'marital_status' => intval(I('post.marital_status', 0))
            ];
            
            if ($obj->saveRequirements($data)) {
                $this->success('保存成功', U('postrequirements/index', ['notice_id' => $notice_id]));
            } else {
                $this->error('保存失败');
            }
        }
        
        $this->assign('notice', $notice);
        $this->assign('post', $post);
        $this->assign('requirements', $requirements);
        $this->assign('gender_options', $obj->gender);
        $this->assign('education_options', $obj->education_level);
        $this->assign('afraid_heights_options', $obj->is_afraid_heights);
        $this->assign('marital_options', $obj->marital_status);
        
        $this->display();
    }

    /**
     * 批量配置岗位要求
     */
    public function batch_config()
    {
        $notice_id = intval(I('get.notice_id'));
        if (!$notice_id) {
            $this->error('参数错误');
        }
        
        // 获取招聘公告信息
        $notice = D('RecruitmentNotice')->where(['id' => $notice_id])->find();
        if (!$notice) {
            $this->error('招聘公告不存在');
        }
        
        // 获取公告关联的岗位
        $posts = D('RecruitmentNotice')->getNoticePosts($notice_id);
        
        if (IS_POST) {
            $post_ids = I('post.post_ids', []);
            if (empty($post_ids)) {
                $this->error('请选择要配置的岗位');
            }
            
            $requirements = [
                'min_age' => intval(I('post.min_age')) ?: null,
                'max_age' => intval(I('post.max_age')) ?: null,
                'gender' => intval(I('post.gender', 0)),
                'min_height' => intval(I('post.min_height')) ?: null,
                'max_height' => intval(I('post.max_height')) ?: null,
                'education_level' => intval(I('post.education_level', 0)),
                'major_keywords' => trim(I('post.major_keywords')),
                'min_work_years' => intval(I('post.min_work_years')) ?: null,
                'max_work_years' => intval(I('post.max_work_years')) ?: null,
                'health_status' => trim(I('post.health_status')),
                'is_afraid_heights' => intval(I('post.is_afraid_heights', 0)),
                'political_status' => trim(I('post.political_status')),
                'marital_status' => intval(I('post.marital_status', 0))
            ];
            
            $obj = D('PostRequirements');
            if ($obj->batchSaveRequirements($post_ids, $notice_id, $requirements)) {
                $this->success('批量配置成功', U('postrequirements/index', ['notice_id' => $notice_id]));
            } else {
                $this->error('批量配置失败');
            }
        }
        
        $obj = D('PostRequirements');
        $this->assign('notice', $notice);
        $this->assign('posts', $posts);
        $this->assign('gender_options', $obj->gender);
        $this->assign('education_options', $obj->education_level);
        $this->assign('afraid_heights_options', $obj->is_afraid_heights);
        $this->assign('marital_options', $obj->marital_status);
        
        $this->display();
    }

    /**
     * 复制要求配置
     */
    public function copy_requirements()
    {
        $notice_id = intval(I('post.notice_id'));
        $from_post_id = intval(I('post.from_post_id'));
        $to_post_ids = I('post.to_post_ids', []);
        
        if (!$notice_id || !$from_post_id || empty($to_post_ids)) {
            $this->error('参数错误');
        }
        
        $obj = D('PostRequirements');
        
        // 获取源岗位的要求配置
        $sourceRequirements = $obj->getRequirements($from_post_id, $notice_id);
        if (!$sourceRequirements) {
            $this->error('源岗位未配置要求');
        }
        
        // 复制到目标岗位
        $success = 0;
        foreach ($to_post_ids as $to_post_id) {
            if ($to_post_id == $from_post_id) {
                continue; // 跳过自己
            }
            
            $data = $sourceRequirements;
            unset($data['id'], $data['create_time'], $data['update_time']);
            $data['post_id'] = $to_post_id;
            
            if ($obj->saveRequirements($data)) {
                $success++;
            }
        }
        
        $this->success("成功复制到 {$success} 个岗位");
    }

    /**
     * 删除岗位要求
     */
    public function delete()
    {
        $notice_id = intval(I('get.notice_id'));
        $post_id = intval(I('get.post_id'));
        
        if (!$notice_id || !$post_id) {
            $this->error('参数错误');
        }
        
        $obj = D('PostRequirements');
        if ($obj->deleteRequirements($post_id, $notice_id)) {
            // 同时删除相关的匹配记录
            D('ResumePostMatch')->deleteMatches($notice_id, $post_id);
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }

    /**
     * 预览筛选结果
     */
    public function preview()
    {
        $notice_id = intval(I('get.notice_id'));
        $post_id = intval(I('get.post_id'));
        
        if (!$notice_id || !$post_id) {
            $this->error('参数错误');
        }
        
        // 获取岗位要求
        $requirements = D('PostRequirements')->getRequirements($post_id, $notice_id);
        if (!$requirements) {
            $this->error('该岗位未配置筛选要求');
        }
        
        // 执行筛选预览
        $matchedResumeIds = D('PostRequirements')->filterResumes($requirements);
        
        // 获取简历详情
        $resumes = [];
        if (!empty($matchedResumeIds)) {
            $resumes = D('UserJob')->where(['id' => ['in', $matchedResumeIds]])
                ->field('id, name, gender, birthdate, phone, education_level, major, height')
                ->limit(50) // 限制预览数量
                ->select();
                
            foreach ($resumes as &$resume) {
                if ($resume['birthdate']) {
                    $resume['age'] = date('Y') - date('Y', strtotime($resume['birthdate']));
                }
            }
        }
        
        $this->assign('requirements', $requirements);
        $this->assign('resumes', $resumes);
        $this->assign('total_count', count($matchedResumeIds));
        
        $this->display();
    }
}
