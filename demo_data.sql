-- 招聘公告智能筛选系统演示数据
-- 请在完成系统部署后执行此SQL来插入演示数据

-- 1. 插入演示招聘公告
INSERT INTO `z_recruitment_notice` (`title`, `description`, `company_name`, `status`, `create_time`, `update_time`) VALUES
('2025年春季校园招聘公告', '我公司现面向全国高校招聘优秀毕业生，提供多个岗位选择，欢迎有志青年加入我们的团队。我们提供具有竞争力的薪酬待遇、完善的培训体系和广阔的发展空间。', '某大型国有企业', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('技术人才专项招聘', '为满足公司快速发展需要，现面向社会招聘各类技术人才，包括软件开发、系统运维、数据分析等岗位。要求具备相关专业背景和工作经验。', '某科技有限公司', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('生产一线操作工招聘', '因生产规模扩大，现招聘生产一线操作工若干名。工作地点在工厂车间，要求身体健康，能适应倒班工作。提供食宿，待遇优厚。', '某制造企业', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 2. 插入演示岗位要求配置（需要根据实际的岗位ID调整）
-- 注意：这里的post_id和notice_id需要根据实际数据库中的ID进行调整

-- 示例：为第一个招聘公告配置岗位要求
-- INSERT INTO `z_post_requirements` (
--     `post_id`, `notice_id`, `min_age`, `max_age`, `gender`, `min_height`, `max_height`, 
--     `education_level`, `major_keywords`, `min_work_years`, `max_work_years`, 
--     `health_status`, `is_afraid_heights`, `political_status`, `marital_status`, 
--     `create_time`, `update_time`
-- ) VALUES
-- (1, 1, 22, 35, 0, 160, 185, 3, '计算机,软件工程,信息技术', 0, 5, '身体健康', 0, '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
-- (2, 1, 20, 30, 1, 170, 190, 2, '机械,自动化,电气', 1, 3, '身体健康,无色盲', 1, '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
-- (3, 1, 18, 45, 0, 155, 180, 1, '', 0, 10, '身体健康,能适应倒班', 0, '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 3. 使用说明
-- 执行完上述SQL后，请按以下步骤操作：

-- 步骤1：登录后台管理系统
-- 步骤2：进入"招聘公告管理"菜单
-- 步骤3：为每个招聘公告关联相应的岗位
-- 步骤4：配置每个岗位的具体筛选要求
-- 步骤5：执行简历匹配

-- 4. 岗位要求配置示例说明

-- 软件开发岗位要求示例：
-- - 年龄：22-35岁
-- - 性别：不限
-- - 身高：160-185cm
-- - 学历：本科及以上
-- - 专业：计算机、软件工程、信息技术相关
-- - 工作经验：0-5年
-- - 健康状况：身体健康
-- - 恐高：不限
-- - 政治面貌：不限
-- - 婚姻状况：不限

-- 机械操作岗位要求示例：
-- - 年龄：20-30岁
-- - 性别：男
-- - 身高：170-190cm
-- - 学历：大专及以上
-- - 专业：机械、自动化、电气相关
-- - 工作经验：1-3年
-- - 健康状况：身体健康，无色盲
-- - 恐高：不能恐高
-- - 政治面貌：不限
-- - 婚姻状况：不限

-- 生产工人岗位要求示例：
-- - 年龄：18-45岁
-- - 性别：不限
-- - 身高：155-180cm
-- - 学历：中专及以上
-- - 专业：不限
-- - 工作经验：0-10年
-- - 健康状况：身体健康，能适应倒班
-- - 恐高：不限
-- - 政治面貌：不限
-- - 婚姻状况：不限

-- 5. 测试数据验证
-- 插入数据后，可以通过以下SQL验证：

-- 查看招聘公告
-- SELECT * FROM z_recruitment_notice;

-- 查看岗位要求配置
-- SELECT * FROM z_post_requirements;

-- 查看岗位关联
-- SELECT * FROM z_recruitment_notice_post;

-- 6. 注意事项
-- 1. 请确保系统已正确部署并且数据库表已创建
-- 2. 执行前请备份数据库
-- 3. 根据实际情况调整岗位ID和公告ID
-- 4. 可以根据需要修改演示数据的内容
-- 5. 建议先在测试环境中验证功能正常后再在生产环境使用

-- 7. 清理演示数据（如需要）
-- DELETE FROM z_resume_post_match WHERE notice_id IN (SELECT id FROM z_recruitment_notice WHERE title LIKE '%演示%' OR title LIKE '%测试%');
-- DELETE FROM z_post_requirements WHERE notice_id IN (SELECT id FROM z_recruitment_notice WHERE title LIKE '%演示%' OR title LIKE '%测试%');
-- DELETE FROM z_recruitment_notice_post WHERE notice_id IN (SELECT id FROM z_recruitment_notice WHERE title LIKE '%演示%' OR title LIKE '%测试%');
-- DELETE FROM z_recruitment_notice WHERE title LIKE '%演示%' OR title LIKE '%测试%';
